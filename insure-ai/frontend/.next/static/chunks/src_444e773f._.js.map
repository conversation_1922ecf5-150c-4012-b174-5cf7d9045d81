{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/insure-ai/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  login: async (credentials: { username: string; password: string }) => {\n    const response = await api.post('/api/auth/login', credentials);\n    return response.data;\n  },\n  \n  register: async (userData: { username: string; email: string; password: string }) => {\n    const response = await api.post('/api/auth/register', userData);\n    return response.data;\n  },\n  \n  getCurrentUser: async () => {\n    const response = await api.get('/api/auth/me');\n    return response.data;\n  },\n  \n  logout: async () => {\n    await api.post('/api/auth/logout');\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  }\n};\n\n// Documents API\nexport const documentsAPI = {\n  upload: async (file: File) => {\n    const formData = new FormData();\n    formData.append('file', file);\n    \n    const response = await api.post('/api/documents/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  },\n  \n  getAll: async () => {\n    const response = await api.get('/api/documents/');\n    return response.data;\n  },\n  \n  getById: async (id: string) => {\n    const response = await api.get(`/api/documents/${id}`);\n    return response.data;\n  },\n  \n  process: async (id: string) => {\n    const response = await api.post(`/api/documents/${id}/process`);\n    return response.data;\n  },\n  \n  extractData: async (documentId: string, templateId: string) => {\n    const response = await api.post(`/api/documents/${documentId}/extract?template_id=${templateId}`);\n    return response.data;\n  },\n  \n  delete: async (id: string) => {\n    await api.delete(`/api/documents/${id}`);\n  }\n};\n\n// Forms API\nexport const formsAPI = {\n  upload: async (file: File, name: string, description?: string) => {\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('name', name);\n    if (description) {\n      formData.append('description', description);\n    }\n    \n    const response = await api.post('/api/forms/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  },\n  \n  getAll: async () => {\n    const response = await api.get('/api/forms/');\n    return response.data;\n  },\n  \n  getById: async (id: string) => {\n    const response = await api.get(`/api/forms/${id}`);\n    return response.data;\n  },\n  \n  detectFields: async (id: string) => {\n    const response = await api.post(`/api/forms/${id}/detect-fields`);\n    return response.data;\n  },\n  \n  addField: async (formId: string, fieldData: any) => {\n    const response = await api.post(`/api/forms/${formId}/fields`, fieldData);\n    return response.data;\n  },\n  \n  updateField: async (fieldId: string, fieldData: any) => {\n    const response = await api.put(`/api/forms/fields/${fieldId}`, fieldData);\n    return response.data;\n  },\n  \n  deleteField: async (fieldId: string) => {\n    await api.delete(`/api/forms/fields/${fieldId}`);\n  },\n  \n  delete: async (id: string) => {\n    await api.delete(`/api/forms/${id}`);\n  }\n};\n\n// Templates API\nexport const templatesAPI = {\n  getAll: async () => {\n    const response = await api.get('/api/templates/');\n    return response.data;\n  },\n  \n  getSystem: async () => {\n    const response = await api.get('/api/templates/system');\n    return response.data;\n  },\n  \n  getById: async (id: string) => {\n    const response = await api.get(`/api/templates/${id}`);\n    return response.data;\n  },\n  \n  create: async (templateData: any) => {\n    const response = await api.post('/api/templates/', templateData);\n    return response.data;\n  },\n  \n  update: async (id: string, templateData: any) => {\n    const response = await api.put(`/api/templates/${id}`, templateData);\n    return response.data;\n  },\n  \n  delete: async (id: string) => {\n    await api.delete(`/api/templates/${id}`);\n  }\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,6DAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,6CAA6C;AAC7C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,sBAAsB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,IAAI,IAAI,CAAC;QACf,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;AACF;AAGO,MAAM,eAAe;IAC1B,QAAQ,OAAO;QACb,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,yBAAyB,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO,YAAoB;QACtC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE,WAAW,qBAAqB,EAAE,YAAY;QAChG,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,IAAI,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;IACzC;AACF;AAGO,MAAM,WAAW;IACtB,QAAQ,OAAO,MAAY,MAAc;QACvC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,QAAQ;QACxB,IAAI,aAAa;YACf,SAAS,MAAM,CAAC,eAAe;QACjC;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB,UAAU;YAC7D,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,cAAc,CAAC;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU,OAAO,QAAgB;QAC/B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,EAAE;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO,SAAiB;QACnC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,SAAS,EAAE;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,IAAI,MAAM,CAAC,CAAC,kBAAkB,EAAE,SAAS;IACjD;IAEA,QAAQ,OAAO;QACb,MAAM,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IACrC;AACF;AAGO,MAAM,eAAe;IAC1B,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;QACT,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,IAAI,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;IACzC;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/insure-ai/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, LoginCredentials, RegisterData, AuthResponse } from '@/types';\nimport { authAPI } from '@/lib/api';\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (credentials: LoginCredentials) => Promise<void>;\n  register: (userData: RegisterData) => Promise<void>;\n  logout: () => Promise<void>;\n  refreshUser: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const isAuthenticated = !!user;\n\n  // Initialize auth state\n  useEffect(() => {\n    const initAuth = async () => {\n      const token = localStorage.getItem('token');\n      const savedUser = localStorage.getItem('user');\n\n      if (token && savedUser) {\n        try {\n          // Verify token is still valid\n          const currentUser = await authAPI.getCurrentUser();\n          setUser(currentUser);\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n        }\n      }\n      setIsLoading(false);\n    };\n\n    initAuth();\n  }, []);\n\n  const login = async (credentials: LoginCredentials) => {\n    try {\n      const response: AuthResponse = await authAPI.login(credentials);\n      \n      // Save token and user to localStorage\n      localStorage.setItem('token', response.access_token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      setUser(response.user);\n    } catch (error: any) {\n      throw new Error(error.response?.data?.detail || 'Login failed');\n    }\n  };\n\n  const register = async (userData: RegisterData) => {\n    try {\n      const response: AuthResponse = await authAPI.register(userData);\n      \n      // Save token and user to localStorage\n      localStorage.setItem('token', response.access_token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      setUser(response.user);\n    } catch (error: any) {\n      throw new Error(error.response?.data?.detail || 'Registration failed');\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      // Continue with logout even if API call fails\n    } finally {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      setUser(null);\n    }\n  };\n\n  const refreshUser = async () => {\n    try {\n      const currentUser = await authAPI.getCurrentUser();\n      setUser(currentUser);\n      localStorage.setItem('user', JSON.stringify(currentUser));\n    } catch (error) {\n      // If refresh fails, logout user\n      await logout();\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    refreshUser,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,CAAC,CAAC;IAE1B,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;mDAAW;oBACf,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,MAAM,YAAY,aAAa,OAAO,CAAC;oBAEvC,IAAI,SAAS,WAAW;wBACtB,IAAI;4BACF,8BAA8B;4BAC9B,MAAM,cAAc,MAAM,oHAAA,CAAA,UAAO,CAAC,cAAc;4BAChD,QAAQ;wBACV,EAAE,OAAO,OAAO;4BACd,kCAAkC;4BAClC,aAAa,UAAU,CAAC;4BACxB,aAAa,UAAU,CAAC;wBAC1B;oBACF;oBACA,aAAa;gBACf;;YAEA;QACF;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAyB,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YAEnD,sCAAsC;YACtC,aAAa,OAAO,CAAC,SAAS,SAAS,YAAY;YACnD,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAEzD,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,UAAU;QAClD;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,WAAyB,MAAM,oHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;YAEtD,sCAAsC;YACtC,aAAa,OAAO,CAAC,SAAS,SAAS,YAAY;YACnD,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAEzD,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAY;YACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,UAAU;QAClD;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM;QACtB,EAAE,OAAO,OAAO;QACd,8CAA8C;QAChD,SAAU;YACR,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,QAAQ;QACV;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,cAAc,MAAM,oHAAA,CAAA,UAAO,CAAC,cAAc;YAChD,QAAQ;YACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,gCAAgC;YAChC,MAAM;QACR;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA/FgB;KAAA;AAiGT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}