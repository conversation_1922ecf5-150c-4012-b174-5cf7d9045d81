{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/insure-ai/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { documentsAPI, formsAPI, templatesAPI } from '@/lib/api';\nimport { Document, Form, Template } from '@/types';\nimport {\n  DocumentTextIcon,\n  DocumentIcon,\n  RectangleStackIcon,\n  PlusIcon,\n  ArrowUpTrayIcon,\n} from '@heroicons/react/24/outline';\n\ninterface DashboardStats {\n  documents: number;\n  forms: number;\n  templates: number;\n  recentDocuments: Document[];\n  recentForms: Form[];\n}\n\nexport default function DashboardPage() {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    documents: 0,\n    forms: 0,\n    templates: 0,\n    recentDocuments: [],\n    recentForms: [],\n  });\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        const [documents, forms, templates] = await Promise.all([\n          documentsAPI.getAll(),\n          formsAPI.getAll(),\n          templatesAPI.getAll(),\n        ]);\n\n        setStats({\n          documents: documents.length,\n          forms: forms.length,\n          templates: templates.length,\n          recentDocuments: documents.slice(0, 5),\n          recentForms: forms.slice(0, 5),\n        });\n      } catch (error) {\n        console.error('Failed to fetch dashboard data:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n  }, []);\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Welcome back, {user?.username}!\n          </h1>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Here's what's happening with your insurance document processing.\n          </p>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-3\">\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <DocumentTextIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Documents\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {stats.documents}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <Link\n                href=\"/documents\"\n                className=\"font-medium text-blue-700 hover:text-blue-900\"\n              >\n                View all documents\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <DocumentIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Forms\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {stats.forms}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <Link\n                href=\"/forms\"\n                className=\"font-medium text-blue-700 hover:text-blue-900\"\n              >\n                View all forms\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <RectangleStackIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Templates\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {stats.templates}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <Link\n                href=\"/templates\"\n                className=\"font-medium text-blue-700 hover:text-blue-900\"\n              >\n                View all templates\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n            Quick Actions\n          </h3>\n          <div className=\"mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n            <Link\n              href=\"/documents/upload\"\n              className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-gray-400\"\n            >\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white\">\n                  <ArrowUpTrayIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium\">\n                  <span className=\"absolute inset-0\" />\n                  Upload Document\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-500\">\n                  Upload a new insurance document for processing\n                </p>\n              </div>\n            </Link>\n\n            <Link\n              href=\"/forms/upload\"\n              className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-gray-400\"\n            >\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white\">\n                  <DocumentIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium\">\n                  <span className=\"absolute inset-0\" />\n                  Upload Form\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-500\">\n                  Upload a blank form for field detection\n                </p>\n              </div>\n            </Link>\n\n            <Link\n              href=\"/templates/create\"\n              className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-gray-400\"\n            >\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white\">\n                  <PlusIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium\">\n                  <span className=\"absolute inset-0\" />\n                  Create Template\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-500\">\n                  Create a new extraction template\n                </p>\n              </div>\n            </Link>\n\n            <Link\n              href=\"/documents\"\n              className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-gray-400\"\n            >\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-orange-50 text-orange-700 ring-4 ring-white\">\n                  <DocumentTextIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium\">\n                  <span className=\"absolute inset-0\" />\n                  Process Documents\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-500\">\n                  Extract data from uploaded documents\n                </p>\n              </div>\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n        {/* Recent Documents */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n              Recent Documents\n            </h3>\n            <div className=\"mt-5\">\n              {stats.recentDocuments.length > 0 ? (\n                <ul className=\"divide-y divide-gray-200\">\n                  {stats.recentDocuments.map((document) => (\n                    <li key={document.id} className=\"py-3\">\n                      <div className=\"flex items-center space-x-4\">\n                        <div className=\"flex-shrink-0\">\n                          <DocumentTextIcon className=\"h-6 w-6 text-gray-400\" />\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium text-gray-900 truncate\">\n                            {document.original_filename}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {new Date(document.created_at).toLocaleDateString()}\n                          </p>\n                        </div>\n                        <div className=\"flex-shrink-0\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            document.status === 'completed'\n                              ? 'bg-green-100 text-green-800'\n                              : document.status === 'processing'\n                              ? 'bg-yellow-100 text-yellow-800'\n                              : document.status === 'error'\n                              ? 'bg-red-100 text-red-800'\n                              : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {document.status}\n                          </span>\n                        </div>\n                      </div>\n                    </li>\n                  ))}\n                </ul>\n              ) : (\n                <p className=\"text-sm text-gray-500\">No documents uploaded yet.</p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Forms */}\n        <div className=\"bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n              Recent Forms\n            </h3>\n            <div className=\"mt-5\">\n              {stats.recentForms.length > 0 ? (\n                <ul className=\"divide-y divide-gray-200\">\n                  {stats.recentForms.map((form) => (\n                    <li key={form.id} className=\"py-3\">\n                      <div className=\"flex items-center space-x-4\">\n                        <div className=\"flex-shrink-0\">\n                          <DocumentIcon className=\"h-6 w-6 text-gray-400\" />\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium text-gray-900 truncate\">\n                            {form.name}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            {form.fields.length} fields detected\n                          </p>\n                        </div>\n                        <div className=\"flex-shrink-0\">\n                          <span className=\"text-sm text-gray-500\">\n                            {new Date(form.created_at).toLocaleDateString()}\n                          </span>\n                        </div>\n                      </div>\n                    </li>\n                  ))}\n                </ul>\n              ) : (\n                <p className=\"text-sm text-gray-500\">No forms uploaded yet.</p>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAuBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,WAAW;QACX,OAAO;QACP,WAAW;QACX,iBAAiB,EAAE;QACnB,aAAa,EAAE;IACjB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,IAAI;gBACF,MAAM,CAAC,WAAW,OAAO,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACtD,iHAAA,CAAA,eAAY,CAAC,MAAM;oBACnB,iHAAA,CAAA,WAAQ,CAAC,MAAM;oBACf,iHAAA,CAAA,eAAY,CAAC,MAAM;iBACpB;gBAED,SAAS;oBACP,WAAW,UAAU,MAAM;oBAC3B,OAAO,MAAM,MAAM;oBACnB,WAAW,UAAU,MAAM;oBAC3B,iBAAiB,UAAU,KAAK,CAAC,GAAG;oBACpC,aAAa,MAAM,KAAK,CAAC,GAAG;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAmC;gCAChC,MAAM;gCAAS;;;;;;;sCAEhC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAO9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;;;;;;sDAE9B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,8OAAC;wDAAG,WAAU;kEACX,MAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,8OAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mOAAA,CAAA,qBAAkB;gDAAC,WAAU;;;;;;;;;;;sDAEhC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAG3D,8OAAC;wDAAG,WAAU;kEACX,MAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAG5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDACC,cAAA,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC,6NAAA,CAAA,kBAAe;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;;;;;;wDAAqB;;;;;;;8DAGvC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAM9C,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDACC,cAAA,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;;;;;;wDAAqB;;;;;;;8DAGvC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAM9C,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDACC,cAAA,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;;;;;;wDAAqB;;;;;;;8DAGvC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAM9C,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDACC,cAAA,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;;;;;;wDAAqB;;;;;;;8DAGvC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAG5D,8OAAC;oCAAI,WAAU;8CACZ,MAAM,eAAe,CAAC,MAAM,GAAG,kBAC9B,8OAAC;wCAAG,WAAU;kDACX,MAAM,eAAe,CAAC,GAAG,CAAC,CAAC,yBAC1B,8OAAC;gDAAqB,WAAU;0DAC9B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;gEAAC,WAAU;;;;;;;;;;;sEAE9B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,SAAS,iBAAiB;;;;;;8EAE7B,8OAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;;;;;;sEAGrD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAW,CAAC,wEAAwE,EACxF,SAAS,MAAM,KAAK,cAChB,gCACA,SAAS,MAAM,KAAK,eACpB,kCACA,SAAS,MAAM,KAAK,UACpB,4BACA,6BACJ;0EACC,SAAS,MAAM;;;;;;;;;;;;;;;;;+CAvBf,SAAS,EAAE;;;;;;;;;6DA+BxB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;kCAO7C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAG5D,8OAAC;oCAAI,WAAU;8CACZ,MAAM,WAAW,CAAC,MAAM,GAAG,kBAC1B,8OAAC;wCAAG,WAAU;kDACX,MAAM,WAAW,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;gDAAiB,WAAU;0DAC1B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,KAAK,IAAI;;;;;;8EAEZ,8OAAC;oEAAE,WAAU;;wEACV,KAAK,MAAM,CAAC,MAAM;wEAAC;;;;;;;;;;;;;sEAGxB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;+CAf5C,KAAK,EAAE;;;;;;;;;6DAuBpB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/insure-ai/frontend/node_modules/%40heroicons/react/24/outline/esm/PlusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/insure-ai/frontend/node_modules/%40heroicons/react/24/outline/esm/ArrowUpTrayIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ArrowUpTrayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowUpTrayIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}