{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/insure-ai/frontend/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login');\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/insure-ai/frontend/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport {\n  DocumentTextIcon,\n  DocumentIcon,\n  RectangleStackIcon,\n  UserIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n} from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: DocumentTextIcon },\n  { name: 'Documents', href: '/documents', icon: DocumentIcon },\n  { name: 'Forms', href: '/forms', icon: DocumentIcon },\n  { name: 'Templates', href: '/templates', icon: RectangleStackIcon },\n];\n\nexport default function Navigation() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const { user, logout } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleLogout = async () => {\n    await logout();\n    router.push('/login');\n  };\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 justify-between\">\n          <div className=\"flex\">\n            <div className=\"flex flex-shrink-0 items-center\">\n              <Link href=\"/dashboard\" className=\"text-xl font-bold text-blue-600\">\n                FormAir\n              </Link>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium ${\n                      isActive\n                        ? 'border-blue-500 text-gray-900'\n                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'\n                    }`}\n                  >\n                    <item.icon className=\"mr-2 h-4 w-4\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </div>\n          </div>\n\n          <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n            <div className=\"relative ml-3\">\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm text-gray-700\">\n                  Welcome, {user?.username}\n                </span>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center rounded-md bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"mr-2 h-4 w-4\" />\n                  Logout\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"-mr-2 flex items-center sm:hidden\">\n            <button\n              type=\"button\"\n              className=\"inline-flex items-center justify-center rounded-md bg-white p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {mobileMenuOpen ? (\n                <XMarkIcon className=\"block h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"block h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {mobileMenuOpen && (\n        <div className=\"sm:hidden\">\n          <div className=\"space-y-1 pb-3 pt-2\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`block border-l-4 py-2 pl-3 pr-4 text-base font-medium ${\n                    isActive\n                      ? 'border-blue-500 bg-blue-50 text-blue-700'\n                      : 'border-transparent text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800'\n                  }`}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <div className=\"flex items-center\">\n                    <item.icon className=\"mr-3 h-5 w-5\" />\n                    {item.name}\n                  </div>\n                </Link>\n              );\n            })}\n          </div>\n          <div className=\"border-t border-gray-200 pb-3 pt-4\">\n            <div className=\"flex items-center px-4\">\n              <div className=\"flex-shrink-0\">\n                <UserIcon className=\"h-8 w-8 text-gray-400\" />\n              </div>\n              <div className=\"ml-3\">\n                <div className=\"text-base font-medium text-gray-800\">\n                  {user?.username}\n                </div>\n                <div className=\"text-sm font-medium text-gray-500\">\n                  {user?.email}\n                </div>\n              </div>\n            </div>\n            <div className=\"mt-3 space-y-1\">\n              <button\n                onClick={handleLogout}\n                className=\"block w-full px-4 py-2 text-left text-base font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-800\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAgBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IAChE;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,uNAAA,CAAA,eAAY;IAAC;IAC5D;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,uNAAA,CAAA,eAAY;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,mOAAA,CAAA,qBAAkB;IAAC;CACnE;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAA<PERSON>,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAkC;;;;;;;;;;;8CAItE,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI;wCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,kEAAkE,EAC5E,WACI,kCACA,8EACJ;;8DAEF,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CATL,KAAK,IAAI;;;;;oCAYpB;;;;;;;;;;;;sCAIJ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDAAwB;gDAC5B,MAAM;;;;;;;sDAElB,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,iPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;sCAO9D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB,CAAC;;kDAElC,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,+BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9B,gCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sDAAsD,EAChE,WACI,6CACA,+FACJ;gCACF,SAAS,IAAM,kBAAkB;0CAEjC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;;;;;;+BAXP,KAAK,IAAI;;;;;wBAepB;;;;;;kCAEF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,MAAM;;;;;;0DAET,8OAAC;gDAAI,WAAU;0DACZ,MAAM;;;;;;;;;;;;;;;;;;0CAIb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/insure-ai/frontend/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client';\n\nimport ProtectedRoute from '@/components/ProtectedRoute';\nimport Navigation from '@/components/Navigation';\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <ProtectedRoute>\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,qBACE,8OAAC,oIAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,gIAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}