import * as util from "../core/util.js";
const Sizable = {
    string: { unit: "ký tự", verb: "có" },
    file: { unit: "byte", verb: "có" },
    array: { unit: "phần tử", verb: "có" },
    set: { unit: "phần tử", verb: "có" },
};
function getSizing(origin) {
    return Sizable[origin] ?? null;
}
export const parsedType = (data) => {
    const t = typeof data;
    switch (t) {
        case "number": {
            return Number.isNaN(data) ? "NaN" : "số";
        }
        case "object": {
            if (Array.isArray(data)) {
                return "mảng";
            }
            if (data === null) {
                return "null";
            }
            if (Object.getPrototypeOf(data) !== Object.prototype && data.constructor) {
                return data.constructor.name;
            }
        }
    }
    return t;
};
const Nouns = {
    regex: "đầu vào",
    email: "địa chỉ email",
    url: "URL",
    emoji: "emoji",
    uuid: "UUID",
    uuidv4: "UUIDv4",
    uuidv6: "UUIDv6",
    nanoid: "nanoid",
    guid: "GUID",
    cuid: "cuid",
    cuid2: "cuid2",
    ulid: "ULID",
    xid: "XID",
    ksuid: "KSUID",
    datetime: "ngày giờ ISO",
    date: "ngày ISO",
    time: "giờ ISO",
    duration: "khoảng thời gian ISO",
    ipv4: "địa chỉ IPv4",
    ipv6: "địa chỉ IPv6",
    cidrv4: "dải IPv4",
    cidrv6: "dải IPv6",
    base64: "chuỗi mã hóa base64",
    base64url: "chuỗi mã hóa base64url",
    json_string: "chuỗi JSON",
    e164: "số E.164",
    jwt: "JWT",
    template_literal: "đầu vào",
};
const error = (issue) => {
    switch (issue.code) {
        case "invalid_type":
            return `Đầu vào không hợp lệ: mong đợi ${issue.expected}, nhận được ${parsedType(issue.input)}`;
        case "invalid_value":
            if (issue.values.length === 1)
                return `Đầu vào không hợp lệ: mong đợi ${util.stringifyPrimitive(issue.values[0])}`;
            return `Tùy chọn không hợp lệ: mong đợi một trong các giá trị ${util.joinValues(issue.values, "|")}`;
        case "too_big": {
            const adj = issue.inclusive ? "<=" : "<";
            const sizing = getSizing(issue.origin);
            if (sizing)
                return `Quá lớn: mong đợi ${issue.origin ?? "giá trị"} ${sizing.verb} ${adj}${issue.maximum.toString()} ${sizing.unit ?? "phần tử"}`;
            return `Quá lớn: mong đợi ${issue.origin ?? "giá trị"} ${adj}${issue.maximum.toString()}`;
        }
        case "too_small": {
            const adj = issue.inclusive ? ">=" : ">";
            const sizing = getSizing(issue.origin);
            if (sizing) {
                return `Quá nhỏ: mong đợi ${issue.origin} ${sizing.verb} ${adj}${issue.minimum.toString()} ${sizing.unit}`;
            }
            return `Quá nhỏ: mong đợi ${issue.origin} ${adj}${issue.minimum.toString()}`;
        }
        case "invalid_format": {
            const _issue = issue;
            if (_issue.format === "starts_with")
                return `Chuỗi không hợp lệ: phải bắt đầu bằng "${_issue.prefix}"`;
            if (_issue.format === "ends_with")
                return `Chuỗi không hợp lệ: phải kết thúc bằng "${_issue.suffix}"`;
            if (_issue.format === "includes")
                return `Chuỗi không hợp lệ: phải bao gồm "${_issue.includes}"`;
            if (_issue.format === "regex")
                return `Chuỗi không hợp lệ: phải khớp với mẫu ${_issue.pattern}`;
            return `${Nouns[_issue.format] ?? issue.format} không hợp lệ`;
        }
        case "not_multiple_of":
            return `Số không hợp lệ: phải là bội số của ${issue.divisor}`;
        case "unrecognized_keys":
            return `Khóa không được nhận dạng: ${util.joinValues(issue.keys, ", ")}`;
        case "invalid_key":
            return `Khóa không hợp lệ trong ${issue.origin}`;
        case "invalid_union":
            return "Đầu vào không hợp lệ";
        case "invalid_element":
            return `Giá trị không hợp lệ trong ${issue.origin}`;
        default:
            return `Đầu vào không hợp lệ`;
    }
};
export { error };
export default function () {
    return {
        localeError: error,
    };
}
