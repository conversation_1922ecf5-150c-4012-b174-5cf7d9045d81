'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { documentsAPI, templatesAPI } from '@/lib/api';
import { Document, Template, ExtractedData, DocumentStatus } from '@/types';
import {
  ArrowLeftIcon,
  PlayIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

export default function DocumentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const documentId = params.id as string;

  const [document, setDocument] = useState<Document | null>(null);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [extractedData, setExtractedData] = useState<ExtractedData[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);

  useEffect(() => {
    if (documentId) {
      fetchDocument();
      fetchTemplates();
      fetchExtractedData();
    }
  }, [documentId]);

  const fetchDocument = async () => {
    try {
      const data = await documentsAPI.getById(documentId);
      setDocument(data);
    } catch (error) {
      console.error('Failed to fetch document:', error);
      router.push('/documents');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      const data = await templatesAPI.getAll();
      setTemplates(data);
    } catch (error) {
      console.error('Failed to fetch templates:', error);
    }
  };

  const fetchExtractedData = async () => {
    try {
      const data = await documentsAPI.getById(documentId);
      // In a real app, you'd have a separate endpoint for extracted data
      // For now, we'll just use an empty array
      setExtractedData([]);
    } catch (error) {
      console.error('Failed to fetch extracted data:', error);
    }
  };

  const handleProcessDocument = async () => {
    if (!document) return;
    
    setIsProcessing(true);
    try {
      await documentsAPI.process(document.id);
      await fetchDocument();
    } catch (error) {
      console.error('Failed to process document:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExtractData = async () => {
    if (!document || !selectedTemplate) return;
    
    setIsExtracting(true);
    try {
      await documentsAPI.extractData(document.id, selectedTemplate);
      await fetchExtractedData();
    } catch (error) {
      console.error('Failed to extract data:', error);
    } finally {
      setIsExtracting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case DocumentStatus.PROCESSING:
        return 'bg-yellow-100 text-yellow-800';
      case DocumentStatus.ERROR:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">Document not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The document you're looking for doesn't exist.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => router.back()}
          className="flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back
        </button>
        <h1 className="text-2xl font-semibold text-gray-900">
          {document.original_filename}
        </h1>
      </div>

      {/* Document Info */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(document.status)}`}>
                  {document.status}
                </span>
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">File Size</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {formatFileSize(document.file_size)}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Content Type</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {document.content_type}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Uploaded</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {new Date(document.created_at).toLocaleDateString()}
              </dd>
            </div>
          </div>

          {/* Process Document */}
          {document.status === DocumentStatus.UPLOADED && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={handleProcessDocument}
                disabled={isProcessing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {isProcessing ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <PlayIcon className="h-4 w-4 mr-2" />
                )}
                {isProcessing ? 'Processing...' : 'Process Document'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Extracted Text */}
      {document.extracted_text && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Extracted Text
            </h3>
            <div className="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                {document.extracted_text}
              </pre>
            </div>
          </div>
        </div>
      )}

      {/* Data Extraction */}
      {document.status === DocumentStatus.COMPLETED && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Extract Structured Data
            </h3>
            <div className="flex items-center space-x-4">
              <select
                value={selectedTemplate}
                onChange={(e) => setSelectedTemplate(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="">Select a template</option>
                {templates.map((template) => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </select>
              <button
                onClick={handleExtractData}
                disabled={!selectedTemplate || isExtracting}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                {isExtracting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <DocumentTextIcon className="h-4 w-4 mr-2" />
                )}
                {isExtracting ? 'Extracting...' : 'Extract Data'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {document.processing_error && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-red-900 mb-4">
              Processing Error
            </h3>
            <div className="bg-red-50 rounded-md p-4">
              <p className="text-sm text-red-700">
                {document.processing_error}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
